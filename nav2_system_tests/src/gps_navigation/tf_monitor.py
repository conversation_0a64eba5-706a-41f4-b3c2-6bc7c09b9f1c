#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
TF转换链监控节点

持续监控关键TF转换链的状态，只有当所有必需的TF转换都可用时，
才发布TF就绪事件，触发导航系统的配置和激活。
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, DurabilityPolicy
from std_msgs.msg import Bool, String
import tf2_ros
import tf2_py
from geometry_msgs.msg import TransformStamped
import threading
import time


class TfMonitor(Node):
    def __init__(self):
        super().__init__('tf_monitor')

        # 参数
        self.declare_parameter('required_transforms', [
            'map->odom',
            'odom->base_link',
            'base_link->base_scan',
            'base_link->imu_link',
            'base_link->gps_link'
        ])
        self.declare_parameter('check_frequency', 2.0)
        self.declare_parameter('transform_timeout', 2.0)
        self.declare_parameter('wait_for_transform_timeout', 1.0)

        self.required_transforms = self.get_parameter('required_transforms').value
        self.check_frequency = self.get_parameter('check_frequency').value
        self.transform_timeout = self.get_parameter('transform_timeout').value
        self.wait_timeout = self.get_parameter('wait_for_transform_timeout').value

        # TF转换状态跟踪
        self.transform_status = {tf_name: False for tf_name in self.required_transforms}
        self.last_successful = {tf_name: None for tf_name in self.required_transforms}

        # TF2 buffer和listener
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer, self)

        # 发布TF状态
        self.status_publisher = self.create_publisher(Bool, '/tf_ready', 10)
        self.detail_publisher = self.create_publisher(String, '/tf_status_detail', 10)

        # 状态标志
        self.all_transforms_ready = False
        self.ready_event_published = False
        self.exit_when_ready = True  # TF就绪时自动退出

        # 定时器 - 定期检查TF转换状态
        self.timer = self.create_timer(1.0 / self.check_frequency, self.check_transforms)

        self.get_logger().info(f'TF monitor started. Required transforms: {self.required_transforms}')
        self.get_logger().info(f'Check frequency: {self.check_frequency} Hz')
        self.get_logger().info(f'Transform timeout: {self.transform_timeout} seconds')

    def parse_transform_name(self, tf_name):
        """解析转换名称，返回源框架和目标框架"""
        if '->' in tf_name:
            parts = tf_name.split('->')
            if len(parts) == 2:
                return parts[0].strip(), parts[1].strip()

        self.get_logger().error(f'Invalid transform name format: {tf_name}. Expected format: "source->target"')
        return None, None

    def check_transform_available(self, source_frame, target_frame):
        """检查指定的TF转换是否可用"""
        try:
            # 检查转换是否存在
            if self.tf_buffer.can_transform(
                target_frame,
                source_frame,
                rclpy.time.Time(),
                timeout=rclpy.duration.Duration(seconds=self.wait_timeout)
            ):
                # 尝试获取转换
                transform = self.tf_buffer.lookup_transform(
                    target_frame,
                    source_frame,
                    rclpy.time.Time(),
                    timeout=rclpy.duration.Duration(seconds=self.wait_timeout)
                )

                # 检查转换的时间戳
                current_time = self.get_clock().now()
                transform_time = rclpy.time.Time.from_msg(transform.header.stamp)

                # 对于静态转换，时间戳通常为0，这是正常的
                if transform_time.nanoseconds == 0:
                    return True, "OK (static transform)"

                # 对于动态转换，检查时间戳是否足够新
                time_diff = (current_time - transform_time).nanoseconds / 1e9

                if time_diff <= self.transform_timeout:
                    return True, f"OK (age: {time_diff:.2f}s)"
                else:
                    return False, f"STALE (age: {time_diff:.2f}s)"
            else:
                return False, "NOT_AVAILABLE"

        except (tf2_ros.LookupException, tf2_ros.ConnectivityException,
                tf2_ros.ExtrapolationException, tf2_py.TransformException) as e:
            return False, f"ERROR: {str(e)[:50]}..."
        except Exception as e:
            return False, f"UNKNOWN_ERROR: {str(e)[:50]}..."

    def check_transforms(self):
        """定期检查TF转换状态"""
        current_time = self.get_clock().now()

        # 检查每个必需的转换
        for tf_name in self.required_transforms:
            source_frame, target_frame = self.parse_transform_name(tf_name)

            if source_frame is None or target_frame is None:
                self.transform_status[tf_name] = False
                continue

            is_available, status_msg = self.check_transform_available(source_frame, target_frame)

            if is_available and not self.transform_status[tf_name]:
                # 转换刚刚变为可用
                self.transform_status[tf_name] = True
                self.last_successful[tf_name] = current_time
                self.get_logger().info(f'✅ Transform {tf_name} is now READY ({status_msg})')

            elif not is_available and self.transform_status[tf_name]:
                # 转换失效
                self.transform_status[tf_name] = False
                self.get_logger().warn(f'❌ Transform {tf_name} is NOT READY ({status_msg})')

            elif is_available:
                # 更新最后成功时间
                self.last_successful[tf_name] = current_time

        # 检查是否所有转换都就绪
        all_ready = all(self.transform_status.values())

        if all_ready and not self.all_transforms_ready:
            # 所有转换刚刚变为就绪状态
            self.all_transforms_ready = True
            self.get_logger().info('✅ ALL TF TRANSFORMS ARE READY! Publishing ready event...')
            self.publish_ready_event()

            # 如果设置为TF就绪时退出，则退出节点
            if self.exit_when_ready:
                # 延迟退出，确保消息发布完成
                self.create_timer(1.0, self.exit_node)

        elif not all_ready and self.all_transforms_ready:
            # 有转换失效
            self.all_transforms_ready = False
            self.ready_event_published = False
            not_ready = [name for name, status in self.transform_status.items() if not status]
            self.get_logger().warning(f'TF transforms not ready: {not_ready}')

        # 发布状态信息
        self.publish_status()

    def publish_ready_event(self):
        """发布TF就绪事件"""
        if not self.ready_event_published:
            # 发布布尔状态
            ready_msg = Bool()
            ready_msg.data = True
            self.status_publisher.publish(ready_msg)

            # 发布详细状态
            detail_msg = String()
            detail_msg.data = "ALL_TF_TRANSFORMS_READY"
            self.detail_publisher.publish(detail_msg)

            self.ready_event_published = True

            # 持续发布就绪状态，确保订阅者能收到
            for i in range(5):  # 发布5次确保可靠性
                self.status_publisher.publish(ready_msg)
                time.sleep(0.1)

    def publish_status(self):
        """发布当前TF转换状态"""
        # 发布布尔状态
        ready_msg = Bool()
        ready_msg.data = self.all_transforms_ready
        self.status_publisher.publish(ready_msg)

        # 发布详细状态
        detail_msg = String()
        status_list = [f"{name}:{'✓' if status else '✗'}" for name, status in self.transform_status.items()]
        detail_msg.data = f"[{', '.join(status_list)}]"
        self.detail_publisher.publish(detail_msg)

    def exit_node(self):
        """退出节点以触发OnExecutionComplete事件"""
        self.get_logger().info('✅ TF monitor task completed. Exiting...')
        # 停止定时器
        if hasattr(self, 'timer'):
            self.timer.cancel()
        # 触发节点退出
        import sys
        sys.exit(0)


def main(args=None):
    rclpy.init(args=args)

    tf_monitor = TfMonitor()

    try:
        rclpy.spin(tf_monitor)
    except KeyboardInterrupt:
        pass
    finally:
        tf_monitor.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
