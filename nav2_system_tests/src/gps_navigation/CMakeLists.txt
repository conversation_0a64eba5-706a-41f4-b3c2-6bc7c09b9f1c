ament_add_test(test_gps_waypoint_follower
  GENERATE_RESULT_FOR_RETURN_CODE_ZERO
  COMMAND "${CMAKE_CURRENT_SOURCE_DIR}/test_case_py.launch.py"
  WORKING_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}"
  TIMEOUT 180
  ENV
    TEST_DIR=${CMAKE_CURRENT_SOURCE_DIR}
)

# 安装 Python 脚本
install(PROGRAMS
  environment_detector_node.py
  map_manager_node.py
  robot_localization_lifecycle_wrapper.py
  indoor_ekf_lifecycle_wrapper.py
  amcl_auto_initializer.py
  sensor_monitor.py
  tf_monitor.py
  nav_monitor.py
  DESTINATION lib/${PROJECT_NAME}
)

# 安装配置文件
install(DIRECTORY
  config
  DESTINATION share/${PROJECT_NAME}
)

# 安装启动文件
install(FILES
  dual_ekf_navsat.launch.py
  event_driven_launch.py
  dual_ekf_navsat_params.yaml
  nav2_no_map_params.yaml
  indoor_ekf_params.yaml
  DESTINATION share/${PROJECT_NAME}
)
