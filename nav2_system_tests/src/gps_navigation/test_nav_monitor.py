#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
测试导航监控节点的生命周期监控功能
"""

import rclpy
from rclpy.node import Node
import subprocess
import time
import sys

class NavMonitorTester(Node):
    def __init__(self):
        super().__init__('nav_monitor_tester')

        self.get_logger().info('Navigation Monitor Tester started')

    def check_nav2_nodes_lifecycle(self):
        """检查Navigation2节点的生命周期状态"""
        # 与启动文件中的lifecycle_nodes顺序保持一致
        nav2_nodes = [
            'controller_server',
            'smoother_server',
            'planner_server',
            'behavior_server',
            'velocity_smoother',
            'collision_monitor',
            'bt_navigator',
            'waypoint_follower'
        ]

        self.get_logger().info('检查Navigation2节点生命周期状态:')
        all_active = True

        for node in nav2_nodes:
            try:
                # 检查lifecycle状态
                result = subprocess.run(
                    ['ros2', 'lifecycle', 'get', f'/{node}'],
                    capture_output=True, text=True, timeout=10
                )

                if result.returncode == 0:
                    status_output = result.stdout.strip()
                    if "active [3]" in status_output:
                        self.get_logger().info(f'  ✅ {node}: active')
                    elif "inactive" in status_output:
                        self.get_logger().warning(f'  ❌ {node}: inactive')
                        all_active = False
                    elif "unconfigured" in status_output:
                        self.get_logger().warning(f'  ⚠️ {node}: unconfigured')
                        all_active = False
                    else:
                        self.get_logger().info(f'  ❓ {node}: {status_output}')
                        all_active = False
                else:
                    # 对于lifecycle_manager，尝试检查is_active服务
                    if 'lifecycle_manager' in node:
                        result = subprocess.run(
                            ['ros2', 'service', 'call', f'/{node}/is_active', 'std_srvs/srv/Trigger'],
                            capture_output=True, text=True, timeout=10
                        )
                        if result.returncode == 0 and 'success: true' in result.stdout:
                            self.get_logger().info(f'  ✅ {node}: active (manager)')
                        else:
                            self.get_logger().warning(f'  ❌ {node}: not active (manager)')
                            all_active = False
                    else:
                        self.get_logger().warning(f'  ❌ {node}: lifecycle service not available')
                        all_active = False

            except Exception as e:
                self.get_logger().error(f'  ❌ {node}: 错误 - {str(e)}')
                all_active = False

        return all_active

    def check_nav2_actions(self):
        """检查Navigation2动作服务器"""
        nav2_actions = [
            '/compute_path_to_pose',
            '/follow_path',
            '/navigate_to_pose',
            '/navigate_through_poses',
            '/backup',
            '/spin',
            '/wait'
        ]

        self.get_logger().info('检查Navigation2动作服务器:')
        all_available = True

        for action in nav2_actions:
            try:
                result = subprocess.run(
                    ['ros2', 'action', 'list'],
                    capture_output=True, text=True, timeout=10
                )

                if result.returncode == 0:
                    if action in result.stdout:
                        self.get_logger().info(f'  ✅ {action}: available')
                    else:
                        self.get_logger().warning(f'  ❌ {action}: not available')
                        all_available = False
                else:
                    self.get_logger().warning(f'  ❌ {action}: failed to check')
                    all_available = False

            except Exception as e:
                self.get_logger().error(f'  ❌ {action}: 错误 - {str(e)}')
                all_available = False

        return all_available

    def run_test(self):
        """运行测试"""
        self.get_logger().info('='*60)
        self.get_logger().info('开始测试Navigation2系统状态')
        self.get_logger().info('='*60)

        # 检查节点生命周期状态
        lifecycle_ok = self.check_nav2_nodes_lifecycle()

        self.get_logger().info('')

        # 检查动作服务器
        actions_ok = self.check_nav2_actions()

        self.get_logger().info('')
        self.get_logger().info('='*60)

        if lifecycle_ok and actions_ok:
            self.get_logger().info('🎉 所有Navigation2组件都已正常启动并处于active状态！')
            return True
        else:
            self.get_logger().warning('⚠️ 部分Navigation2组件未正常启动')
            if not lifecycle_ok:
                self.get_logger().warning('  - 生命周期状态检查失败')
            if not actions_ok:
                self.get_logger().warning('  - 动作服务器检查失败')
            return False

def main(args=None):
    rclpy.init(args=args)

    tester = NavMonitorTester()

    try:
        # 运行测试
        success = tester.run_test()

        if success:
            tester.get_logger().info('测试通过！导航监控应该能够正确检测到所有组件就绪。')
        else:
            tester.get_logger().warning('测试未通过。请检查Navigation2系统状态。')

    except KeyboardInterrupt:
        pass
    finally:
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
